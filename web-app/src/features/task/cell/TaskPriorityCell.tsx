import { GridRenderCellParams } from '@mui/x-data-grid';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import { TaskPriority, TaskPriorityIconColorMap, TaskRead } from '../taskTypes';
import Cell from '../../../components/Cell';

type TaskPriorityCellParam = GridRenderCellParams<TaskRead, TaskPriority | undefined, string>;

export default function TaskPriorityCell(params: TaskPriorityCellParam) {
  const { value: priority, formattedValue: priorityName } = params;

  if (!priority || priority === TaskPriority.LOW) {
    return null;
  }

  return (
    <Cell title={priorityName}>
      <PriorityHighIcon fontSize="small" sx={{ color: TaskPriorityIconColorMap[priority] }} />
    </Cell>
  );
}
