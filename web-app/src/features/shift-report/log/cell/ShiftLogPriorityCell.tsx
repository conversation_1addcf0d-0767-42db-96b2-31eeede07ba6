import { GridRenderCellParams } from '@mui/x-data-grid';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import { ShiftLogRead, ShiftLogPriority, ShiftLogPriorityIconMap, ShiftLogPriorityDisplayMap } from '../shiftLogTypes';
import Cell from '../../../../components/Cell';

export default function ShiftLogPriorityCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const priority = row.priority;
  const priorityName = ShiftLogPriorityDisplayMap[priority];

  if (priority !== ShiftLogPriority.HIGH) {
    return null;
  }

  return (
    <Cell title={priorityName}>
      <PriorityHighIcon fontSize="small" sx={{ color: ShiftLogPriorityIconMap[priority], mx: 0.75 }} />
    </Cell>
  );
}
